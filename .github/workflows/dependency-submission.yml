name: Dependency Submission

on:
  push:
    branches: ['main']
  workflow_dispatch:

permissions:
  contents: write

concurrency:
  group: ${{ github.workflow }}${{ github.ref_name != github.event.repository.default_branch && github.ref || github.run_id }}
  cancel-in-progress: ${{ github.ref_name != github.event.repository.default_branch }}

jobs:
  dependency-submission:
    name: Dependency Submission
    runs-on: ubuntu-latest
    steps:
      - name: Checkout sources
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
      - uses: actions/setup-java@c5195efecf7bdfc987ee8bae7a71cb8b11521c00 # v4.7.1
        name: Setup Java
        with:
          distribution: temurin
          java-version: 21
      - name: Generate and submit dependency graph
        uses: gradle/actions/dependency-submission@ac638b010cf58a27ee6c972d7336334ccaf61c96 # v4.4.1
        with:
          gradle-version: wrapper
          dependency-graph: generate-and-submit
          # Include only relevant configurations
          dependency-graph-include-configurations: '(implementation|api|compileClasspath|runtimeClasspath)'
