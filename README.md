# Software Engineer Backend Coding Interview

## Requirements
* JDK 21

## Local Setup

### Running the Application using IntelliJ IDEA

1. Clone this repository
2. Import the project as a new project in IntelliJ
3. Navigate to `GetYourGuideApplication.java` and run the main method by clicking the top green arrow on the left side of the editor.

![IntelliJ Run Screenshot](resources/intellij_screenshot.png)

### Running the Application using Gradle

ℹ️ Note: We recommend running the application using IntelliJ IDEA so you can leverage its debug tooling.

1. Clone this repository
2. Navigate to the project directory in your terminal
3. Run the following command: `./gradlew bootRun`

### Running the application using another IDE
You are welcome to use any IDE of your preference to run the application. As long as you are able to execute the application, access its endpoints, and utilize a debugger if necessary, we support the use of any IDE that meets these requirements. Please bear in mind that your interviewer may not be familiar with your IDE, so may not be able to support you as effectively during the interview if necessary.

The application endpoints will be accessible on `http://localhost:8080/`.
